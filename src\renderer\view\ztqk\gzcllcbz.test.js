    wrapper = shallowMount(gzcllcbz, {
      mocks: {
        $route: mockRoute,
      },
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  describe('xyb method', () => {
    it('should update form and call saveFaultHandling', async () => {
      const mockSaveFaultHandling = jest.fn().mockResolvedValue({ code: 10000, data: '123' });
      wrapper.vm.saveFaultHandling = mockSaveFaultHandling;
      wrapper.vm.form = {
        equipmentCode: 'EQ001',
        maintenanceStatus: '',
        deleteFlag: 1,
        newId: '',
        id: '',
      };
      wrapper.vm.active = 1;
      wrapper.vm.pageloading = true;

      await wrapper.vm.xyb();

      expect(wrapper.vm.form.maintenanceStatus).toBe(1);
      expect(wrapper.vm.form.deleteFlag).toBe(0);
      expect(wrapper.vm.form.newId).toBe('1');
      expect(mockSaveFaultHandling).toHaveBeenCalledWith(wrapper.vm.form);
      expect(wrapper.vm.form.id).toBe('123');
      expect(wrapper.vm.pageloading).toBe(false);
      expect(wrapper.vm.active).toBe(2);
    });

    it('should handle saveFaultHandling failure', async () => {
      const mockSaveFaultHandling = jest.fn().mockResolvedValue({ code: 50000 });
      wrapper.vm.saveFaultHandling = mockSaveFaultHandling;
      wrapper.vm.form = {
        equipmentCode: 'EQ001',
        maintenanceStatus: '',
        deleteFlag: 1,
        newId: '',
        id: '',
      };
      wrapper.vm.active = 1;
      wrapper.vm.pageloading = true;

      await wrapper.vm.xyb();

      expect(wrapper.vm.form.maintenanceStatus).toBe(1);
      expect(wrapper.vm.form.deleteFlag).toBe(0);
      expect(wrapper.vm.form.newId).toBe('1');
      expect(mockSaveFaultHandling).toHaveBeenCalledWith(wrapper.vm.form);
      expect(wrapper.vm.form.id).toBe('');
      expect(wrapper.vm.pageloading).toBe(false);
      expect(wrapper.vm.active).toBe(2);
    });

    it('should handle missing matched item', async () => {
      const mockSaveFaultHandling = jest.fn().mockResolvedValue({ code: 10000, data: '123' });
      wrapper.vm.saveFaultHandling = mockSaveFaultHandling;
      wrapper.vm.form = {
        equipmentCode: 'EQ003',
        maintenanceStatus: '',
        deleteFlag: 1,
        newId: '',
        id: '',
      };
      wrapper.vm.active = 1;
      wrapper.vm.pageloading = true;

      await wrapper.vm.xyb();

      expect(wrapper.vm.form.maintenanceStatus).toBe(1);
      expect(wrapper.vm.form.deleteFlag).toBe(0);
      expect(wrapper.vm.form.newId).toBeUndefined();
      expect(mockSaveFaultHandling).toHaveBeenCalledWith(wrapper.vm.form);
      expect(wrapper.vm.form.id).toBe('123');
      expect(wrapper.vm.pageloading).toBe(false);
      expect(wrapper.vm.active).toBe(2);
    });
  });
});
