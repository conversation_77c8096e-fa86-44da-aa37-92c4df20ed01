<!--  -->
<template>
  <div class="box" v-loading="pageloading">
    <div class="top-box">
      <div>
        <img src="./img/title.png" alt="" />
      </div>
      <div class="top-title">设备变更信息</div>
    </div>
    <div>
      <el-form
        class="inputcss"
        ref="form"
        :model="form"
        label-width="100px"
        label-position="left"
        style="margin-top: 20px"
      >
        <div style="display: flex">
          <el-form-item label="设备名称" style="margin-left: 20px">
            <el-input v-model="form.sbmc"></el-input>
          </el-form-item>
          <el-form-item label="设备编号" style="margin-left: 20px">
            <el-input v-model="form.sbbh"></el-input>
          </el-form-item>
          <div class="cxbtn" @click="getequipmentByRoomList">查询</div>
          <!-- <div class="buttonw btnc3" @click="batchOpenCabinet">批量开柜门</div> -->
          <div class="buttonw btnc2" @click="dcbutton()">机柜信息</div>
        </div>
      </el-form>
      <!-- <div class="buttonw btnc1" @click="savetj()">提交</div> -->
    </div>

    <div style="width: 100%; margin-top: 20px">
      <el-table
        ref="table"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="tableHeaderCellStyle"
        :cell-style="tableCellStyle"
        max-height="800px"
      >
        <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
        <el-table-column type="index" label="序号" width="60" align="center">
        </el-table-column>
        <el-table-column prop="cabinetCode" label="机柜编号" align="center">
        </el-table-column>
        <el-table-column prop="equipmentCode" label="设备编号" align="center">
        </el-table-column>
        <el-table-column prop="equipmentName" label="设备名称" align="center">
        </el-table-column>
        <el-table-column prop="destroyStatus" label="变更状态" align="center">
        </el-table-column>
        <el-table-column label="柜门状态" align="center" width="100">
          <template slot-scope="scope">
            <span
              :style="{
                color: scope.row.flag === 1 ? 'red' : 'rgb(51, 51, 51)',
              }"
            >
              {{ scope.row.flag === 1 ? "开启" : "关闭" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <!-- :disabled="scope.row.flag === 1" -->
              <el-button
                type="primary"
                size="mini"
                :disabled="scope.row.flag === 1"
                @click="openSingleCabinet(scope.row)"
              >
                开柜门
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 15px">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-pageNo="page"
          :pageNo-sizes="[5, 10, 20, 30]"
          :pageNo-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 机柜信息弹框 -->
    <el-dialog
      title="机柜信息"
      :visible.sync="dialogVisible"
      width="70%"
      :before-close="handleClose"
      v-loading="pageloading"
    >
      <div style="margin: 0 0 20px 0; padding: 0">
        <el-form
          :inline="true"
          :model="searchForm"
          class="demo-form-inline"
          style="border: none"
        >
          <!-- <el-form-item label="机柜编号" style="margin-bottom: 0;">
            <el-input v-model="searchForm.cabinetCode" placeholder="机柜编号" style="width: 180px;"></el-input>
          </el-form-item>
          <el-form-item label="机柜名称" style="margin-bottom: 0;">
            <el-input v-model="searchForm.cabinetName" placeholder="机柜名称" style="width: 180px;"></el-input>
          </el-form-item> -->
          <el-form-item style="margin-bottom: 0">
            <el-button type="primary" @click="commitExport"
              >提交并导出</el-button
            >
          </el-form-item>
          <el-form-item style="margin-bottom: 0">
            <el-button type="primary" @click="freshList"
              >刷新机柜信息</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <el-table
        :data="cabinetData"
        style="width: 100%"
        :header-cell-style="tableHeaderCellStyle"
        :cell-style="tableCellStyle"
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="cabinetCode"
          label="机柜编号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="cabinetName"
          label="机柜名称"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="equipmentCode"
          label="设备编号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="equipmentName"
          label="设备名称"
          align="center"
        ></el-table-column>
        <el-table-column prop="uwStatus" label="U位状态" align="center">
          <template slot-scope="scope">
            <span
              :style="{
                color: scope.row.flag === 0 ? 'red' : 'rgb(51, 51, 51)',
              }"
            >
              {{ scope.row.uwStatus === 0 ? "正常" : "异常" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="flag" label="机柜状态" align="center">
          <template slot-scope="scope">
            <span
              :style="{
                color: scope.row.flag === 1 ? 'red' : 'rgb(51, 51, 51)',
              }"
            >
              {{ scope.row.flag === 1 ? "开启" : "关闭" }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 15px; text-align: right">
        <el-pagination
          background
          @current-change="handleCabinetPageChange"
          @size-change="handleCabinetSizeChange"
          :current-page="cabinetPage"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="cabinetPageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="cabinetTotal"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  queryEquipmentByCondition,
  saveDestructionEquipment,
  exportDestructionEquipment,
  saveMigrateEquipment,
  exportMigrateEquipment,
  getequipmentByRoom,
  getCabinetMmachineStatus,
  openCabinetByPort,
  websocketUrl,
} from "../../../api/jfxj";
import { getScanCodeResult } from "../../../api/shma.js";
export default {
  name: "",
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  data() {
    //这里存放数据
    return {
      form: {
        jfbh: "",
        jfdd: "",
        sblx: "",
        sbbh: "",
        sbxh: "",
        sbpp: "",
        sbxlh: "",
        xhsj: "",
        zrr: "",
        czr: "",
        sbmc: "",
        sbbh: "",
        relocationCabinetCode: "",
        relocationCabinetName: "",
        relocationComputerRoomName: "",
        relocationInstitution: "",
        relocationLocation: "",
        area: "",
      },
      flag: "",
      sfdc: false,
      page: 1,
      pageSize: 10,
      total: 0,
      tableData: [],
      pageloading: false,
      dialogVisible: false,
      cabinetData: [],
      searchForm: {
        cabinetCode: "",
        cabinetName: "",
      },
      cabinetPage: 1,
      cabinetPageSize: 10,
      cabinetTotal: 0,
      websocket: null, // WebSocket连接实例
      reconnectTimer: null, // 重连定时器
      reconnectAttempts: 0, // 重连尝试次数
      maxReconnectAttempts: 5, // 最大重连次数
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    handleCurrentChange() {},
    handleSizeChange() {},
    tableCellStyle() {
      return "font-family: SourceHanSansSC-Normal;font-size: 16px;color: #333333;font-weight: 400;";
    },
    tableHeaderCellStyle() {
      return "font-family: SourceHanSansSC-Normal;font-size: 16px;color: #1766D1;font-weight: 400;background: #D7ECFF;";
    },

    async getequipmentByRoomList() {
      this.pageloading = true;
      try {
        const params = {
          scanCode: this.$route.query.scanCode,
          flag: 2,
          equipmentName: this.form.sbmc,
          equipmentCode: this.form.sbbh,
          pageNum: this.page,
          pageSize: this.pageSize,
        };
        const response = await getequipmentByRoom(params);
        const { code, data } = response;
        if (code === 10000) {
          this.tableData = data.records;
          this.total = data.total;
          this.pageloading = false;
        }
      } catch (error) {
        console.log(error);
        this.pageloading = false;
      }
    },

    async queryEquipmentByCondition() {
      console.log(
        this.$route.query.equipmentId,
        "this.$route.query.equipmentCode"
      );

      queryEquipmentByCondition({
        equipmentCode: this.$route.query.equipmentId,
      }).then((res) => {
        console.log(res);
        this.form = res.data[0];
        this.form.relocationCabinetName =
          this.$route.query.relocationCabinetName;
        this.form.relocationComputerRoomName =
          this.$route.query.relocationComputerRoomName;
        this.form.relocationInstitution =
          this.$route.query.relocationInstitution;
        this.form.relocationLocation = this.$route.query.relocationLocation;
        this.form.area = this.$route.query.area;
        // 创建一个新的Date对象，获取当前日期和时间
        var now = new Date();

        // 获取年、月、日
        var year = now.getFullYear();
        var month = String(now.getMonth() + 1).padStart(2, "0"); // 月份从0开始，需要加1，并且格式化为两位数
        var day = String(now.getDate()).padStart(2, "0"); // 格式化为两位数

        // 组合成年-月-日的格式
        var formattedDate = year + "-" + month + "-" + day;
        this.form.xhsj = formattedDate;
      });
    },

    // 获取机柜列表
    async getCabinetList() {
      // 这里需要调用获取机柜列表的API

      getCabinetMmachineStatus({
        cabinetCode: this.searchForm.cabinetCode,
        cabinetName: this.searchForm.cabinetName,
        pageNum: this.cabinetPage,
        pageSize: this.cabinetPageSize,
      }).then((res) => {
        // console.log(res, '279--------------');
        if (res.code === 10000) {
          this.cabinetData = res.data.records;
          this.cabinetTotal = res.data.total;
        }
      });
    },
    freshList() {
      this.getCabinetList();
    },
    // 关闭弹框
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
          }
          // this.stopWarningTimer();
          done();
        })
        .catch((_) => {});
    },
    // 搜索机柜
    handleSearch() {
      this.cabinetPage = 1;
      this.getCabinetList();
    },
    // 机柜分页变化
    handleCabinetPageChange(val) {
      this.cabinetPage = val;
      this.getCabinetList();
    },
    // 机柜分页大小变化
    handleCabinetSizeChange(val) {
      this.cabinetPageSize = val;
      this.getCabinetList();
    },
    // 关闭WebSocket连接
    closeWebSocket() {
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
      if (this.websocket) {
        this.websocket.close();
        this.websocket = null;
      }
    },
    // 显示警告通知 - 修改为接收WebSocket数据
    showWarningNotification(data) {
      this.$notify({
        title: "系统警告",
        message: data,
        type: "warning",
        duration: 5000, // 设置为0表示不自动关闭
        showClose: true,
        onClose: () => {
          this.warningVisible = false;
        },
      });
    },
    // WebSocket重连
    reconnectWebSocket() {
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        console.log(
          `尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`
        );

        this.reconnectTimer = setTimeout(() => {
          this.initWebSocket();
        }, 3000 * this.reconnectAttempts); // 递增延迟重连
      } else {
        console.error("WebSocket重连次数已达上限");
      }
    },
    // 初始化WebSocket连接
    initWebSocket() {
      try {
        this.websocket = new WebSocket(websocketUrl);

        this.websocket.onopen = () => {
          console.log("WebSocket连接已建立");
          // console.log(this.reconnectAttempts, 'this.reconnectAttempts')
          this.reconnectAttempts = 0; // 重置重连次数
        };

        this.websocket.onmessage = (event) => {
          try {
            const data = event.data;
            this.showWarningNotification(data);
          } catch (error) {
            console.error("解析WebSocket消息失败:", error);
          }
        };

        this.websocket.onclose = () => {
          console.log("WebSocket连接已关闭");
          // this.reconnectWebSocket();
        };

        this.websocket.onerror = (error) => {
          console.error("WebSocket连接错误:", error);
        };
      } catch (error) {
        console.error("创建WebSocket连接失败:", error);
        this.reconnectWebSocket();
      }
    },
    generateAlertMessages(data) {
      return data.map((item) => {
        return `机柜 ${item.cabinetName} 异常：首次开柜时间 ${item.alertStartTime}，最后开柜时间 ${item.alertFinishTime}`;
      });
    },
    // 单个开柜门
    async openSingleCabinet(row) {
      this.pageloading = true;
      let resDatas = await getScanCodeResult({
        scanCode: this.$route.query.scanCode,
      });
      let array1 = [];

      if (resDatas.code == 10000) {
        array1 = resDatas.data.map;
      }
      let obj1 = row;
      const matchedItem = array1.find(
        (item) => item.equipmentCode === obj1.equipmentCode
      );
      if (matchedItem) {
        obj1.newId = matchedItem.id; // 添加 newId 属性
      }
      let params = {
        id: obj1.newId,
        port: row.port,
        type: 3,
        cabinetId: row.cabinetId,
      };

      console.log(params, "params");
      if (row.flag === 1) {
        this.$message.warning("柜门已开启，无需重复操作");

        this.pageloading = false;
        return;
      }

      if (!row.port) {
        this.$message.error("该设备缺少端口信息，无法开启柜门");
        this.pageloading = false;
        return;
      }

      // this.pageloading = true;

      // let params = {
      //   port: [row.port],
      //   cabinetCode: [row.cabinetCode],
      //   id: this.$route.query.queryId,
      //   type:3
      // };

      // let params = {
      //   id: obj1.newId,
      //   port: row.port,
      //   type: 3,
      //   cabinetId: row.cabinetId
      // }

      try {
        const response = await openCabinetByPort(params);
        if (response.code === 10000) {
          this.$message.success("开柜门成功");
          // 刷新列表以更新柜门状态
          this.getequipmentByRoomList();
          this.pageloading = false;
        } else {
          this.$message.error(response.message || "开柜门失败");
          this.pageloading = false;
        }
      } catch (error) {
        this.pageloading = false;
        console.error("开柜门失败：", error);
        this.$message.error("开柜门失败，请稍后重试");
      }
    },

    // 批量开柜门
    // async batchOpenCabinet() {
    //   const selectedRows = this.$refs.table.selection;

    //   if (selectedRows.length === 0) {
    //     this.$message.warning("请先选择要开启柜门的设备");
    //     return;
    //   }

    //   // 过滤出可以开启柜门的设备（柜门状态为关闭且有port字段）
    //   const validRows = selectedRows.filter(
    //     (row) => row.flag !== 1 && row.port
    //   );

    //   if (validRows.length === 0) {
    //     this.$message.warning("所选设备的柜门均已开启或缺少端口信息");
    //     return;
    //   }

    //   // 如果有部分设备不能开启，提示用户
    //   if (validRows.length < selectedRows.length) {
    //     const invalidCount = selectedRows.length - validRows.length;
    //     const confirmResult = await this.$confirm(
    //       `所选设备中有 ${invalidCount} 个设备的柜门已开启或缺少端口信息，是否继续开启其余 ${validRows.length} 个设备的柜门？`,
    //       "提示",
    //       {
    //         confirmButtonText: "确定",
    //         cancelButtonText: "取消",
    //         type: "warning",
    //       }
    //     ).catch(() => false);

    //     if (!confirmResult) {
    //       return;
    //     }
    //   }

    //   try {
    //     const ports = validRows.map((row) => row.port);
    //     const cabinetCodes = validRows.map((row) => row.cabinetCode);

    //     this.pageloading = true;

    //     let params = {
    //       port: ports,
    //       cabinetCode: cabinetCodes,
    //       id: this.$route.query.queryId,
    //       type:3
    //     };
    //     const response = await openCabinetByPort(params);

    //     if (response.code === 10000) {
    //       this.$message.success(`成功开启 ${validRows.length} 个柜门`);
    //       // 刷新列表以更新柜门状态
    //       this.getequipmentByRoomList();

    //       this.pageloading = false;
    //     } else {
    //       this.$message.error(response.message || "批量开柜门失败");
    //       this.pageloading = false;
    //     }
    //   } catch (error) {
    //     console.error("批量开柜门失败：", error);
    //     this.$message.error("批量开柜门失败，请稍后重试");
    //     this.pageloading = false;
    //   }
    // },
    async commitExport() {
      this.pageloading = true;
      let flagStatus = this.cabinetData.filter((item) => item.flag == 1);
      let UWStatus = this.cabinetData.filter((item) => item.uwStatus == 1);
      // if (flagStatus && flagStatus.length > 0) {
      //   this.$message.warning("存在已开启的机柜，禁止导出！");
      //   this.pageloading = false;
      //   return;
      // }
      // if (UWStatus && UWStatus.length > 0) {
      //   this.$message.warning("存在U位异常数据，禁止导出！");
      //   this.pageloading = false;
      //   return;
      // }

      let resDatas = await getScanCodeResult({
        scanCode: this.$route.query.scanCode,
      });

      if (resDatas.code == 10000) {
        console.log(this.tableData, "tableData");
        console.log(resDatas.data.map, "this.$route.query.resDatas");

        let array1 = this.tableData;
        let array2 = resDatas.data.map;

        // 将第二个数组转换为以equipmentCode为键的对象，方便查找
        const array2Map = {};
        array2.forEach((item) => {
          array2Map[item.equipmentCode] = item.id;
        });

        // 遍历第一个数组，添加对应的id
        const result = array1.map((item) => {
          // 如果第二个数组中有对应的equipmentCode，则添加id
          if (array2Map.hasOwnProperty(item.equipmentCode)) {
            return {
              ...item,
              newId: array2Map[item.equipmentCode], // 这里用idFromArray2作为新字段名，避免与原有id冲突
            };
          }
          return item;
        });

        console.log(result, "485---");
        const saveResponse = await saveDestructionEquipment(result);
        if (saveResponse.code !== 10000) {
          this.pageloading = false;
          this.$message.error("保存失败：" + saveResponse.message);
          return;
        }
        // 从保存接口的响应中获取 data 数组
        const idsFromSave = saveResponse.data;
        // 构建导出接口需要的参数数组
        const exportParams = idsFromSave.map((id) => ({
          id: id,
          startTime: this.tableData[0].startTime || "",
        }));
        // 调用导出接口，传递 id 参数数组
        const exportResponse = await exportDestructionEquipment(exportParams);
        if (exportResponse.code !== 10000) {
          this.$message.error(exportResponse.message);
          this.pageloading = false;
          return;
        }

        // 导出接口返回的是文件流，直接处理下载
        this.$message.success("导出成功");
        this.pageloading = false;
      }
      this.getequipmentByRoomList();
    },
    async dcbutton() {
      this.dialogVisible = true;
      this.getCabinetList();
      // 初始化WebSocket连接来监听警告通知
      this.initWebSocket();
      return;
      try {
        // 获取用户选中的行
        const selectedRows = this.$refs.table.selection; // 假设你的 el-table 的 ref 是 "table"
        const formDataArray = [];

        // 如果有选中的行，只传递选中的行
        if (selectedRows.length > 0) {
          selectedRows.forEach((row) => {
            formDataArray.push({
              area: row.area,
              jfbh: row.jfbh,
              jfdd: row.jfdd,
              qydz: row.qydz,
              qysj: row.qysj,
              relocationCabinetCode: row.relocationCabinetCode,
              relocationCabinetName: row.relocationCabinetName,
              relocationComputerRoomName: row.relocationComputerRoomName,
              relocationInstitution: row.relocationInstitution,
              relocationLocation: row.relocationLocation,
              sbbh: row.sbbh,
              sblx: row.sblx,
              sbmc: row.sbmc,
              xbbh: row.xbbh,
              equipmentCode: row.equipmentCode,
              flag: 2,
            });
          });
        } else {
          // 如果没有选中的行，提示用户是否导出全部数据
          await this.$confirm("未选中任何行，是否导出全部数据？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          });

          // 用户确认导出全部数据
          this.tableData.forEach((row) => {
            formDataArray.push({
              area: row.area,
              jfbh: row.jfbh,
              jfdd: row.jfdd,
              qydz: row.qydz,
              qysj: row.qysj,
              relocationCabinetCode: row.relocationCabinetCode,
              relocationCabinetName: row.relocationCabinetName,
              relocationComputerRoomName: row.relocationComputerRoomName,
              relocationInstitution: row.relocationInstitution,
              relocationLocation: row.relocationLocation,
              sbbh: row.sbbh,
              sblx: row.sblx,
              sbmc: row.sbmc,
              xbbh: row.xbbh,
              equipmentCode: row.equipmentCode,
              flag: 2,
            });
          });
        }

        // 如果没有数据可供导出，直接返回
        if (formDataArray.length === 0) {
          this.$message.warning("没有数据可供导出");
          return;
        }

        // 调用保存接口
        const saveResponse = await saveDestructionEquipment(formDataArray);
        if (saveResponse.code !== 10000) {
          this.$message.error("保存失败：" + saveResponse.message);
          return;
        }

        // 从保存接口的响应中获取 data 数组
        const idsFromSave = saveResponse.data;

        // 构建导出接口需要的参数数组
        const exportParams = idsFromSave.map((id) => ({ id: id }));

        // 调用导出接口，传递 id 参数数组
        const exportResponse = await exportDestructionEquipment(exportParams);
        if (exportResponse.code !== 10000) {
          this.$message.error(exportResponse.message);
          return;
        }
        // 导出接口返回的是文件流，直接处理下载
        this.$message.success("导出成功");
      } catch (error) {
        if (error === "cancel") {
          this.$message.info("已取消导出操作");
        } else {
          console.error("导出失败：", error);
          this.$message.error("导出失败，请稍后重试");
        }
      }
      this.getequipmentByRoomList();
    },
  },

  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    // this.queryEquipmentByCondition();
    this.getequipmentByRoomList();
  },
  //生命周期 - 创建之前
  beforeCreate() {},
  //生命周期 - 挂载之前
  beforeMount() {},
  //生命周期 - 更新之前
  beforeUpdate() {},
  //生命周期 - 更新之后
  updated() {},
  //生命周期 - 变更之前
  beforeDestroy() {},
  //生命周期 - 变更完成
  destroyed() {},
  //如果页面有keep-alive缓存功能，这个函数会触发
  activated() {},
};
</script>
<style scoped>
.box {
  width: 1580px;
  margin: 0 auto;
}
.cxbtn {
  width: 72px;
  height: 32px;
  background: #3e9efe;
  border-radius: 2px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0.07px;
  font-weight: 400;
  text-align: center;
  line-height: 32px;
  margin-left: 20px;
  margin-top: 5px;
}
.top-box {
  width: 100%;
  display: flex;
  border-bottom: 1px solid #e5e5e5;
  margin-top: 20px;
}
.top-title {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
  margin-left: 10px;
}
.mg20 {
  margin-right: 20px;
}
.buttonw {
  cursor: pointer;
  width: 123px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 4px;
}
.btnc1 {
  background-color: #3e9efe;
}
.btnc2 {
  background-color: #3ecafe;
  margin-left: 20px;
  margin-top: 5px;
}
.btnc3 {
  background-color: #67c23a;
  margin-left: 20px;
  margin-top: 5px;
}
::v-deep .el-form-item__content {
  width: 350px !important;
}

::v-deep .el-form-item__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 20px;
  color: #080808;
  font-weight: 400;
}

::v-deep .el-form--label-left .el-form-item__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 20px;
  color: #080808;
  font-weight: 400;
}

::v-deep .el-input.is-disabled .el-input__inner {
  font-size: 18px;
}
</style>
